import React from 'react';
import { Minus, Plus, Trash2, ShoppingCart, Percent, Package } from 'lucide-react';
import { toast } from 'sonner';

import { usePOSStore } from '@/stores/posStore';
import { formatCurrency } from '@/lib/utils';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import { Card, CardContent } from '@/components/common/Card';

const Cart: React.FC = () => {
  const {
    cart,
    discountPercentage,
    notes,
    updateQuantity,
    removeFromCart,
    clearCart,
    setDiscount,
    setNotes,
  } = usePOSStore();

  const handleQuantityChange = (productId: number, newQuantity: number) => {
    if (newQuantity < 1) {
      removeFromCart(productId);
      return;
    }
    updateQuantity(productId, newQuantity);
  };

  const handleRemoveItem = (productId: number, productName: string) => {
    removeFromCart(productId);
    toast.success(`${productName} removed from cart`);
  };

  const handleClearCart = () => {
    if (cart.length === 0) return;

    clearCart();
    toast.success('Cart cleared');
  };

  const handleDiscountChange = (value: string) => {
    const percentage = parseFloat(value) || 0;
    setDiscount(percentage);
  };

  if (cart.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <ShoppingCart className="h-16 w-16 mb-4" />
        <h3 className="text-lg font-medium mb-2">Cart is empty</h3>
        <p className="text-sm text-center">Add products to start a transaction</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Cart Items */}
      <div className="space-y-3">
        {cart.map((item) => {
          const { product, quantity, totalPrice, unitPrice } = item;
          const description = product.description || product.sku;

          return (
            <Card key={product.id} className="shadow-soft">
              <CardContent className="p-4">
                <div className="grid grid-cols-[88px,1fr] gap-4">
                  <div className="flex flex-col justify-between">
                    <div className="w-full aspect-square rounded-md bg-gray-100 flex items-center justify-center overflow-hidden">
                      {product.imagePath ? (
                        <img
                          src={product.imagePath}
                          alt={product.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <Package className="h-8 w-8 text-gray-400" />
                      )}
                    </div>
                    <p className="mt-3 text-sm font-semibold text-gray-900">
                      {formatCurrency(totalPrice)}
                    </p>
                  </div>

                  <div className="flex flex-col justify-between gap-4">
                    <div className="flex items-start justify-between gap-3">
                      <div className="min-w-0">
                        <h4 className="text-sm font-semibold text-gray-900 line-clamp-2">
                          {product.name}
                        </h4>
                        {description && (
                          <p className="mt-1 text-xs text-gray-500 line-clamp-2">
                            {description}
                          </p>
                        )}
                        <p className="mt-2 text-sm font-medium text-gray-900">
                          {formatCurrency(unitPrice)}
                          <span className="ml-1 text-xs text-gray-500">
                            x {quantity} {product.unit}
                          </span>
                        </p>
                      </div>

                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveItem(product.id, product.name)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        aria-label={`Remove ${product.name}`}
                      >
                        <Trash2 className="h-10 w-10" />
                      </Button>
                    </div>

                    <div className="flex items-center justify-end gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleQuantityChange(product.id, quantity - 1)}
                        className="h-8 w-8 p-0"
                        aria-label={`Decrease quantity of ${product.name}`}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>

                      <div className="min-w-[3rem] rounded-md border border-secondary-200 bg-white px-3 py-1 text-center text-sm font-semibold text-gray-900">
                        {quantity}
                      </div>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleQuantityChange(product.id, quantity + 1)}
                        className="h-8 w-8 p-0"
                        aria-label={`Increase quantity of ${product.name}`}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Discount Input */}
      <div className="pt-4 border-t">
        <Input
          type="number"
          label="Discount (%)"
          placeholder="0"
          value={discountPercentage.toString()}
          onChange={(e) => handleDiscountChange(e.target.value)}
          leftIcon={<Percent className="h-4 w-4" />}
          min="0"
          max="100"
          step="0.1"
        />
      </div>

      {/* Notes Input */}
      <div>
        <Input
          type="text"
          label="Notes (optional)"
          placeholder="Add transaction notes..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </div>

      {/* Clear Cart Button */}
      <div className="pt-4 border-t">
        <Button
          variant="outline"
          onClick={handleClearCart}
          className="w-full text-red-600 border-red-300 hover:bg-red-50"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear Cart
        </Button>
      </div>
    </div>
  );
};

export default Cart;
