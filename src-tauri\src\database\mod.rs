use sqlx::{<PERSON>, Sqlite, SqlitePool};
use std::path::Path;
use tauri::api::path::app_data_dir;

pub mod migrations;

pub type DbPool = Pool<Sqlite>;

pub async fn create_database_connection() -> Result<DbPool, sqlx::Error> {
    let app_data_path = app_data_dir(&tauri::Config::default())
        .expect("Failed to get app data directory");
    
    std::fs::create_dir_all(&app_data_path)
        .expect("Failed to create app data directory");
    
    let database_url = app_data_path.join("pos.db");
    let database_url = format!("sqlite:{}", database_url.to_string_lossy());
    
    let pool = SqlitePool::connect(&database_url).await?;
    
    // Run migrations
    migrations::run_migrations(&pool).await?;
    
    Ok(pool)
}