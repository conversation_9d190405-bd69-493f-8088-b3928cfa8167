import React from 'react';
import { Plus, Package } from 'lucide-react';
import { toast } from 'sonner';

import { Product } from '@/types';
import { usePOSStore } from '@/stores/posStore';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent } from '@/components/common/Card';
import Button from '@/components/common/Button';

interface ProductGridProps {
  products: Product[];
}

interface ProductCardProps {
  product: Product;
  onAddToCart: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart }) => {
  const isLowStock = product.currentStock <= product.minStock;
  const isOutOfStock = product.currentStock === 0;

  const handleAddToCart = () => {
    if (isOutOfStock && !product.allowNegativeStock) {
      toast.error('Product is out of stock');
      return;
    }
    
    onAddToCart(product);
    toast.success(`${product.name} added to cart`);
  };

  return (
    <Card className="hover:shadow-medium transition-shadow cursor-pointer group">
      <CardContent className="p-4">
        <div className="flex flex-col h-full">
          {/* Product Image Placeholder */}
          <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-3">
            {product.imagePath ? (
              <img
                src={product.imagePath}
                alt={product.name}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <Package className="h-12 w-12 text-gray-400" />
            )}
          </div>

          {/* Product Info */}
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-2">
              {product.name}
            </h3>
            <p className="text-xs text-gray-500 mb-2">{product.sku}</p>
            
            {product.description && (
              <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                {product.description}
              </p>
            )}

            {/* Stock Status */}
            <div className="flex items-center justify-between mb-3">
              <span className="text-xs text-gray-500">
                Stock: {product.currentStock} {product.unit}
              </span>
              {isLowStock && !isOutOfStock && (
                <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                  Low Stock
                </span>
              )}
              {isOutOfStock && (
                <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                  Out of Stock
                </span>
              )}
            </div>

            {/* Price */}
            <div className="mb-3">
              <span className="text-lg font-bold text-gray-900">
                {formatCurrency(product.sellingPrice)}
              </span>
              {product.wholesalePrice && product.wholesalePrice !== product.sellingPrice && (
                <span className="text-sm text-gray-500 ml-2">
                  Wholesale: {formatCurrency(product.wholesalePrice)}
                </span>
              )}
            </div>
          </div>

          {/* Add to Cart Button */}
          <Button
            onClick={handleAddToCart}
            disabled={isOutOfStock && !product.allowNegativeStock}
            size="sm"
            className="w-full group-hover:bg-primary-700"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add to Cart
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

const ProductGrid: React.FC<ProductGridProps> = ({ products }) => {
  const { addToCart } = usePOSStore();

  if (products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Package className="h-16 w-16 mb-4" />
        <h3 className="text-lg font-medium mb-2">No products found</h3>
        <p className="text-sm">Try adjusting your search terms</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          onAddToCart={addToCart}
        />
      ))}
    </div>
  );
};

export default ProductGrid;
